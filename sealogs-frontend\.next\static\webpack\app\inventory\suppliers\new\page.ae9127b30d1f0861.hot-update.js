"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/suppliers/new/page",{

/***/ "(app-pages-browser)/./src/app/ui/inventory/supplier-contacts.tsx":
/*!****************************************************!*\
  !*** ./src/app/ui/inventory/supplier-contacts.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n\n\n\n\n\nfunction SupplierContacts(param) {\n    let { data, setData } = param;\n    const addItem = ()=>{\n        setData((prev)=>{\n            return [\n                ...prev,\n                {\n                    name: \"\",\n                    phone: \"\",\n                    email: \"\"\n                }\n            ];\n        });\n    };\n    const removeItem = (index)=>{\n        setData((prev)=>{\n            const newData = [\n                ...prev\n            ];\n            newData.splice(index, 1);\n            return newData;\n        });\n    };\n    const onValueChange = (field, index, value)=>{\n        setData((prev)=>{\n            const newData = [\n                ...prev\n            ];\n            newData[index][field] = value;\n            return newData;\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-5\",\n        children: [\n            data.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 \".concat(index > 0 ? \"border-t pt-4\" : \"\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                    label: \"Contact Name\",\n                                    htmlFor: \"supplier-contact_name\".concat(index),\n                                    className: \"text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                        id: \"supplier-contact_name\".concat(index),\n                                        type: \"text\",\n                                        placeholder: \"Contact Name\",\n                                        value: item.name,\n                                        onChange: (e)=>onValueChange(\"name\", index, e.target.value),\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                    label: \"Contact Phone\",\n                                    htmlFor: \"supplier-contact_phone\".concat(index),\n                                    className: \"text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                        id: \"supplier-contact_phone\".concat(index),\n                                        type: \"text\",\n                                        placeholder: \"Contact Phone\",\n                                        value: item.phone,\n                                        onChange: (e)=>onValueChange(\"phone\", index, e.target.value),\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                    label: \"Contact Email\",\n                                    htmlFor: \"supplier-contact_email\".concat(index),\n                                    className: \"text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                        id: \"supplier-contact_email\".concat(index),\n                                        type: \"email\",\n                                        value: item.email,\n                                        placeholder: \"Contact Email\",\n                                        onChange: (e)=>onValueChange(\"email\", index, e.target.value),\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 21\n                        }, this),\n                        index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"destructive\",\n                                size: \"sm\",\n                                iconLeft: _barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                onClick: ()=>removeItem(index),\n                                children: \"Remove Contact\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, index, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 17\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end mt-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"outline\",\n                    iconLeft: _barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    onClick: addItem,\n                    children: \"Add Contact\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                lineNumber: 136,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n        lineNumber: 55,\n        columnNumber: 9\n    }, this);\n}\n_c = SupplierContacts;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SupplierContacts);\nvar _c;\n$RefreshReg$(_c, \"SupplierContacts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/inventory/supplier-contacts.tsx\n"));

/***/ })

});