"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/new/page",{

/***/ "(app-pages-browser)/./src/components/file-upload.tsx":
/*!****************************************!*\
  !*** ./src/components/file-upload.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FileUpload; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction FileUpload(param) {\n    let { setDocuments, text = \"Documents and Images\", subText, bgClass = \"\", documents, multipleUpload = true } = param;\n    _s();\n    /* ------------------------------------------------------- */ /* state / refs                                            */ /* ------------------------------------------------------- */ const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentFiles, setCurrentFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openFileNameDialog, setOpenFileNameDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoader, setImageLoader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    /* ------------------------------------------------------- */ /* helpers                                                 */ /* ------------------------------------------------------- */ const dropZoneClasses = (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors focus-visible:outline-none\", dragActive ? \"bg-curious-blue-50 border-curious-blue-400\" : \"bg-muted border-border\", \"text-foreground hover:bg-curious-blue-50 hover:border-curious-blue-300\", \"min-h-[10rem] cursor-pointer select-none\", bgClass);\n    const uploadFile = async (file)=>{\n        const formData = new FormData();\n        formData.append(\"FileData\", file, file.name.replace(/\\s/g, \"\"));\n        try {\n            const response = await fetch(\"\".concat(\"https://api.sealogs.com/api/\", \"v2/upload\"), {\n                method: \"POST\",\n                headers: {\n                    Authorization: \"Bearer \".concat(localStorage.getItem(\"sl-jwt\"))\n                },\n                body: formData\n            });\n            const data = await response.json();\n            await getFileDetails({\n                variables: {\n                    id: [\n                        data[0].id\n                    ]\n                }\n            });\n            setImageLoader(false);\n        } catch (err) {\n            /* eslint-disable-next-line no-console */ console.error(err);\n        }\n    };\n    /* ------------------------------------------------------- */ /* apollo hooks                                            */ /* ------------------------------------------------------- */ const [getFileDetails] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_9__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.GET_FILES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setCurrentFiles((prev)=>[\n                    ...prev,\n                    response.readFiles.nodes[0]\n                ]);\n            setOpenFileNameDialog(true);\n        },\n        onError: (error)=>console.error(error)\n    });\n    const [updateFile] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_10__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UPDATE_FILE, {\n        onCompleted: (response)=>{\n            const updated = response.updateFile;\n            setDocuments((prev)=>multipleUpload ? [\n                    ...prev,\n                    updated\n                ] : [\n                    updated\n                ]);\n        },\n        onError: (error)=>console.error(error)\n    });\n    /* ------------------------------------------------------- */ /* event handlers                                          */ /* ------------------------------------------------------- */ const handleFiles = (fileList)=>{\n        const arr = Array.from(fileList);\n        setImageLoader(true);\n        arr.forEach(uploadFile);\n    };\n    const onChange = (e)=>{\n        if (e.target.files) handleFiles(e.target.files);\n    };\n    const onDrop = (e)=>{\n        e.preventDefault();\n        setDragActive(false);\n        if (e.dataTransfer.files) handleFiles(e.dataTransfer.files);\n    };\n    const onDragToggle = (state)=>(e)=>{\n            e.preventDefault();\n            setDragActive(state);\n        };\n    const handleUpdateFileName = ()=>{\n        currentFiles.forEach((file, index)=>{\n            const newFileName = document.getElementById(\"file-name-\".concat(index)).value;\n            updateFile({\n                variables: {\n                    input: {\n                        id: file.id,\n                        title: newFileName\n                    }\n                }\n            });\n        });\n        setOpenFileNameDialog(false);\n    };\n    const openFileExplorer = ()=>{\n        var _inputRef_current;\n        return (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.click();\n    };\n    /* ------------------------------------------------------- */ /* render                                                  */ /* ------------------------------------------------------- */ return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full pt-4 lg:pt-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: dropZoneClasses,\n                onSubmit: (e)=>e.preventDefault(),\n                onDragEnter: onDragToggle(true),\n                onDragOver: onDragToggle(true),\n                onDragLeave: onDragToggle(false),\n                onDrop: onDrop,\n                onClick: openFileExplorer,\n                \"aria-label\": \"File uploader drop zone\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute top-4 left-4 text-xs font-medium uppercase tracking-wider\",\n                        children: text\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                        ref: inputRef,\n                        type: \"file\",\n                        className: \"hidden\",\n                        multiple: multipleUpload,\n                        accept: \".xlsx,.xls,image/*,.doc,.docx,.ppt,.pptx,.txt,.pdf\",\n                        onChange: onChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-2 pointer-events-none\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: \"/sealogs-document_upload.svg\",\n                                alt: \"Upload illustration\",\n                                width: 96,\n                                height: 96,\n                                className: \"relative -translate-x-2.5\",\n                                priority: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 21\n                            }, this),\n                            subText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-muted-foreground\",\n                                children: subText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                lineNumber: 144,\n                columnNumber: 13\n            }, this),\n            imageLoader ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 flex items-center justify-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-5 w-5 animate-spin text-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"Uploading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                lineNumber: 188,\n                columnNumber: 17\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.AlertDialogNew, {\n                openDialog: openFileNameDialog,\n                setOpenDialog: setOpenFileNameDialog,\n                handleCreate: handleUpdateFileName,\n                actionText: \"Save\",\n                title: \"File Name\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: currentFiles.map((file, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                            label: \"File \".concat(idx + 1, \" Name\"),\n                            htmlFor: \"file-name-\".concat(idx),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                id: \"file-name-\".concat(idx),\n                                defaultValue: file.title,\n                                placeholder: \"Enter file name\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 33\n                            }, this)\n                        }, file.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 29\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                lineNumber: 195,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n        lineNumber: 143,\n        columnNumber: 9\n    }, this);\n}\n_s(FileUpload, \"rxkOJP2/OhEpByV5ygdFSHDBNt8=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_9__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_10__.useMutation\n    ];\n});\n_c = FileUpload;\nvar _c;\n$RefreshReg$(_c, \"FileUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/file-upload.tsx\n"));

/***/ })

});