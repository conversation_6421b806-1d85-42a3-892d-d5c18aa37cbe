"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/new/page",{

/***/ "(app-pages-browser)/./src/components/file-upload.tsx":
/*!****************************************!*\
  !*** ./src/components/file-upload.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FileUpload; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction FileUpload(param) {\n    let { setDocuments, text = \"Documents and Images\", subText, bgClass = \"\", documents, multipleUpload = true } = param;\n    _s();\n    /* ------------------------------------------------------- */ /* state / refs                                            */ /* ------------------------------------------------------- */ const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentFiles, setCurrentFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openFileNameDialog, setOpenFileNameDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoader, setImageLoader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    /* ------------------------------------------------------- */ /* helpers                                                 */ /* ------------------------------------------------------- */ const dropZoneClasses = (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors focus-visible:outline-none\", dragActive ? \"bg-slorange-400/60 border-slorange-600\" : \"bg-slorange-300/40 border-slorange-1000\", \"text-slorange-1000 hover:bg-slorange-400/40\", \"min-h-[10rem] cursor-pointer select-none\", bgClass);\n    const uploadFile = async (file)=>{\n        const formData = new FormData();\n        formData.append(\"FileData\", file, file.name.replace(/\\s/g, \"\"));\n        try {\n            const response = await fetch(\"\".concat(\"https://api.sealogs.com/api/\", \"v2/upload\"), {\n                method: \"POST\",\n                headers: {\n                    Authorization: \"Bearer \".concat(localStorage.getItem(\"sl-jwt\"))\n                },\n                body: formData\n            });\n            const data = await response.json();\n            await getFileDetails({\n                variables: {\n                    id: [\n                        data[0].id\n                    ]\n                }\n            });\n            setImageLoader(false);\n        } catch (err) {\n            /* eslint-disable-next-line no-console */ console.error(err);\n        }\n    };\n    /* ------------------------------------------------------- */ /* apollo hooks                                            */ /* ------------------------------------------------------- */ const [getFileDetails] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.GET_FILES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setCurrentFiles((prev)=>[\n                    ...prev,\n                    response.readFiles.nodes[0]\n                ]);\n            setOpenFileNameDialog(true);\n        },\n        onError: (error)=>console.error(error)\n    });\n    const [updateFile] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UPDATE_FILE, {\n        onCompleted: (response)=>{\n            const updated = response.updateFile;\n            setFiles([]);\n            setDocuments((prev)=>multipleUpload ? [\n                    ...prev,\n                    updated\n                ] : [\n                    updated\n                ]);\n        },\n        onError: (error)=>console.error(error)\n    });\n    /* ------------------------------------------------------- */ /* event handlers                                          */ /* ------------------------------------------------------- */ const handleFiles = (fileList)=>{\n        const arr = Array.from(fileList);\n        setFiles((prev)=>[\n                ...prev,\n                ...arr\n            ]);\n        setImageLoader(true);\n        arr.forEach(uploadFile);\n    };\n    const onChange = (e)=>{\n        if (e.target.files) handleFiles(e.target.files);\n    };\n    const onDrop = (e)=>{\n        e.preventDefault();\n        setDragActive(false);\n        if (e.dataTransfer.files) handleFiles(e.dataTransfer.files);\n    };\n    const onDragToggle = (state)=>(e)=>{\n            e.preventDefault();\n            setDragActive(state);\n        };\n    const handleUpdateFileName = ()=>{\n        currentFiles.forEach((file, index)=>{\n            const newFileName = document.getElementById(\"file-name-\".concat(index)).value;\n            updateFile({\n                variables: {\n                    input: {\n                        id: file.id,\n                        title: newFileName\n                    }\n                }\n            });\n        });\n        setOpenFileNameDialog(false);\n    };\n    const openFileExplorer = ()=>{\n        var _inputRef_current;\n        return (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.click();\n    };\n    /* ------------------------------------------------------- */ /* effects                                                 */ /* ------------------------------------------------------- */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (documents === null || documents === void 0 ? void 0 : documents.length) setFiles((prev)=>[\n                ...prev,\n                ...documents\n            ]);\n    }, [\n        documents\n    ]);\n    /* ------------------------------------------------------- */ /* render                                                  */ /* ------------------------------------------------------- */ return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full pt-4 lg:pt-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: dropZoneClasses,\n                onSubmit: (e)=>e.preventDefault(),\n                onDragEnter: onDragToggle(true),\n                onDragOver: onDragToggle(true),\n                onDragLeave: onDragToggle(false),\n                onDrop: onDrop,\n                \"aria-label\": \"File uploader drop zone\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute top-4 left-4 text-xs font-medium uppercase tracking-wider\",\n                        children: text\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                        ref: inputRef,\n                        type: \"file\",\n                        className: \"hidden\",\n                        multiple: multipleUpload,\n                        accept: \".xlsx,.xls,image/*,.doc,.docx,.ppt,.pptx,.txt,.pdf\",\n                        onChange: onChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        type: \"button\",\n                        variant: \"ghost\",\n                        onClick: openFileExplorer,\n                        className: \"flex flex-col items-center gap-2 h-auto p-0 hover:bg-transparent\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: \"/sealogs-document_upload.svg\",\n                                alt: \"Upload illustration\",\n                                width: 96,\n                                height: 96,\n                                priority: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 21\n                            }, this),\n                            subText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-slate-600\",\n                                children: subText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                lineNumber: 154,\n                columnNumber: 13\n            }, this),\n            imageLoader ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 flex items-center justify-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-5 w-5 animate-spin text-slorange-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-slorange-600\",\n                        children: \"Uploading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                lineNumber: 200,\n                columnNumber: 17\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.AlertDialogNew, {\n                openDialog: openFileNameDialog,\n                setOpenDialog: setOpenFileNameDialog,\n                handleCreate: handleUpdateFileName,\n                actionText: \"Save\",\n                title: \"File Name\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: currentFiles.map((file, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                            label: \"File \".concat(idx + 1, \" Name\"),\n                            htmlFor: \"file-name-\".concat(idx),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                id: \"file-name-\".concat(idx),\n                                defaultValue: file.title,\n                                placeholder: \"Enter file name\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 33\n                            }, this)\n                        }, file.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 29\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                lineNumber: 207,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n        lineNumber: 153,\n        columnNumber: 9\n    }, this);\n}\n_s(FileUpload, \"/F7rhqoG1tD93AQmvJO3DiL1QjM=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation\n    ];\n});\n_c = FileUpload;\nvar _c;\n$RefreshReg$(_c, \"FileUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/file-upload.tsx\n"));

/***/ })

});