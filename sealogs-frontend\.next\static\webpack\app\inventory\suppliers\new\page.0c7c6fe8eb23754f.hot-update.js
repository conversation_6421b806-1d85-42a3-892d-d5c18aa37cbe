"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/suppliers/new/page",{

/***/ "(app-pages-browser)/./src/app/ui/inventory/supplier-contacts.tsx":
/*!****************************************************!*\
  !*** ./src/app/ui/inventory/supplier-contacts.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n\n\n\n\nfunction SupplierContacts(param) {\n    let { data, setData } = param;\n    const addItem = ()=>{\n        setData((prev)=>{\n            return [\n                ...prev,\n                {\n                    name: \"\",\n                    phone: \"\",\n                    email: \"\"\n                }\n            ];\n        });\n    };\n    const removeItem = (index)=>{\n        setData((prev)=>{\n            const newData = [\n                ...prev\n            ];\n            newData.splice(index, 1);\n            return newData;\n        });\n    };\n    const onValueChange = (field, index, value)=>{\n        setData((prev)=>{\n            const newData = [\n                ...prev\n            ];\n            newData[index][field] = value;\n            return newData;\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid md:grid-cols-3 gap-6 pb-4 pt-3 px-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl\",\n                children: [\n                    \"Contact people\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs mt-4 max-w-[25rem] leading-loose\",\n                        children: \"Enter the contact details (name, phone, and email) of the supplier’s representative.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                lineNumber: 56,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:col-span-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex grow flex-col\",\n                        children: data.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex grow flex-col \".concat(index > 0 ? \"border-t mt-4 pt-4\" : \"\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex grow flex-col md:items-center md:flex-row md:col-span-2 lg:col-span-3 lg:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                            id: \"supplier-contact_name\".concat(index),\n                                            type: \"text\",\n                                            placeholder: \"Contact Name\",\n                                            value: item.name,\n                                            onChange: (e)=>onValueChange(\"name\", index, e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                            id: \"supplier-contact_phone\".concat(index),\n                                            type: \"text\",\n                                            placeholder: \"Contact Phone\",\n                                            value: item.phone,\n                                            onChange: (e)=>onValueChange(\"phone\", index, e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                            id: \"supplier-contact_email\".concat(index),\n                                            type: \"email\",\n                                            value: item.email,\n                                            placeholder: \"Contact Email\",\n                                            onChange: (e)=>onValueChange(\"email\", index, e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            type: \"button\",\n                                            className: \"text-sm gap-2 text-red-600 \".concat(index == 0 ? \"md:invisible hidden md:flex\" : \"md:visible flex\"),\n                                            onClick: index == 0 ? ()=>null : ()=>removeItem(index),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 37\n                                                }, this),\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"block md:hidden\",\n                                                    children: \"Remove\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 29\n                                }, this)\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 25\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            type: \"button\",\n                            className: \"group inline-flex justify-center items-center rounded-md bg-transparent px-4 py-2 text-sm text-sky-800 shadow-sm hover:shadow-md hover:bg-white ring-1 ring-sky-700\",\n                            onClick: addItem,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                    className: \"w-5 h-5 -ml-2 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 25\n                                }, this),\n                                \"Add contact\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                lineNumber: 63,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n        lineNumber: 55,\n        columnNumber: 9\n    }, this);\n}\n_c = SupplierContacts;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SupplierContacts);\nvar _c;\n$RefreshReg$(_c, \"SupplierContacts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvaW52ZW50b3J5L3N1cHBsaWVyLWNvbnRhY3RzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBc0M7QUFDTztBQUNFO0FBZS9DLFNBQVNHLGlCQUFpQixLQUFrQztRQUFsQyxFQUFFQyxJQUFJLEVBQUVDLE9BQU8sRUFBbUIsR0FBbEM7SUFDdEIsTUFBTUMsVUFBVTtRQUNaRCxRQUFRLENBQUNFO1lBQ0wsT0FBTzttQkFDQUE7Z0JBQ0g7b0JBQ0lDLE1BQU07b0JBQ05DLE9BQU87b0JBQ1BDLE9BQU87Z0JBQ1g7YUFDSDtRQUNMO0lBQ0o7SUFFQSxNQUFNQyxhQUFhLENBQUNDO1FBQ2hCUCxRQUFRLENBQUNFO1lBQ0wsTUFBTU0sVUFBVTttQkFBSU47YUFBSztZQUN6Qk0sUUFBUUMsTUFBTSxDQUFDRixPQUFPO1lBQ3RCLE9BQU9DO1FBQ1g7SUFDSjtJQUVBLE1BQU1FLGdCQUFnQixDQUNsQkMsT0FDQUosT0FDQUs7UUFFQVosUUFBUSxDQUFDRTtZQUNMLE1BQU1NLFVBQVU7bUJBQUlOO2FBQUs7WUFFekJNLE9BQU8sQ0FBQ0QsTUFBTSxDQUFDSSxNQUFNLEdBQUdDO1lBRXhCLE9BQU9KO1FBQ1g7SUFDSjtJQUVBLHFCQUNJLDhEQUFDSztRQUFJQyxXQUFVOzswQkFDWCw4REFBQ0Q7Z0JBQUlDLFdBQVU7O29CQUFVO2tDQUVyQiw4REFBQ0M7d0JBQUVELFdBQVU7a0NBQTJDOzs7Ozs7Ozs7Ozs7MEJBSzVELDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ1gsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNWZixLQUFLaUIsR0FBRyxDQUFDLENBQUNDLE1BQU1WLHNCQUNiLDhEQUFDTTtnQ0FFR0MsV0FBVyxzQkFBNEQsT0FBdENQLFFBQVEsSUFBSSx1QkFBdUI7MENBQ3BFLDRFQUFDTTtvQ0FBSUMsV0FBVTs7c0RBQ1gsOERBQUNsQix1REFBS0E7NENBQ0ZzQixJQUFJLHdCQUE4QixPQUFOWDs0Q0FDNUJZLE1BQUs7NENBQ0xDLGFBQVk7NENBQ1pSLE9BQU9LLEtBQUtkLElBQUk7NENBQ2hCa0IsVUFBVSxDQUFDQyxJQUNQWixjQUNJLFFBQ0FILE9BQ0FlLEVBQUVDLE1BQU0sQ0FBQ1gsS0FBSzs7Ozs7O3NEQUkxQiw4REFBQ2hCLHVEQUFLQTs0Q0FDRnNCLElBQUkseUJBQStCLE9BQU5YOzRDQUM3QlksTUFBSzs0Q0FDTEMsYUFBWTs0Q0FDWlIsT0FBT0ssS0FBS2IsS0FBSzs0Q0FDakJpQixVQUFVLENBQUNDLElBQ1BaLGNBQ0ksU0FDQUgsT0FDQWUsRUFBRUMsTUFBTSxDQUFDWCxLQUFLOzs7Ozs7c0RBSTFCLDhEQUFDaEIsdURBQUtBOzRDQUNGc0IsSUFBSSx5QkFBK0IsT0FBTlg7NENBQzdCWSxNQUFLOzRDQUNMUCxPQUFPSyxLQUFLWixLQUFLOzRDQUNqQmUsYUFBWTs0Q0FDWkMsVUFBVSxDQUFDQyxJQUNQWixjQUNJLFNBQ0FILE9BQ0FlLEVBQUVDLE1BQU0sQ0FBQ1gsS0FBSzs7Ozs7O3NEQUkxQiw4REFBQ2YseURBQU1BOzRDQUNIc0IsTUFBSzs0Q0FDTEwsV0FBVyw4QkFBNkYsT0FBL0RQLFNBQVMsSUFBSSxnQ0FBZ0M7NENBQ3RGaUIsU0FDSWpCLFNBQVMsSUFDSCxJQUFNLE9BQ04sSUFBTUQsV0FBV0M7OzhEQUUzQiw4REFBQ1osNkVBQUNBO29EQUFDbUIsV0FBVTs7Ozs7O2dEQUFhOzhEQUMxQiw4REFBQ1c7b0RBQUtYLFdBQVU7OERBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7K0JBbkRyQ1A7Ozs7Ozs7Ozs7a0NBMkRqQiw4REFBQ007d0JBQUlDLFdBQVU7a0NBQ1gsNEVBQUNqQix5REFBTUE7NEJBQ0hzQixNQUFLOzRCQUNMTCxXQUFVOzRCQUNWVSxTQUFTdkI7OzhDQUNULDhEQUFDeUI7b0NBQVNaLFdBQVU7Ozs7OztnQ0FBdUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9uRTtLQXhIU2hCO0FBMEhULCtEQUFlQSxnQkFBZ0JBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC91aS9pbnZlbnRvcnkvc3VwcGxpZXItY29udGFjdHMudHN4PzI4N2YiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGx1cywgWCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcclxuaW1wb3J0IHsgSW5wdXQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvaW5wdXQnXHJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nXHJcbmltcG9ydCB7IExhYmVsIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2xhYmVsJ1xyXG5cclxuZXhwb3J0IGludGVyZmFjZSBJU3VwcGxpZXJDb250YWN0IHtcclxuICAgIGlkPzogbnVtYmVyXHJcbiAgICBuYW1lOiBzdHJpbmdcclxuICAgIHBob25lPzogc3RyaW5nXHJcbiAgICBlbWFpbD86IHN0cmluZ1xyXG59XHJcblxyXG5pbnRlcmZhY2UgSUNvbXBvbmVudFByb3BzIHtcclxuICAgIGRhdGE6IElTdXBwbGllckNvbnRhY3RbXVxyXG4gICAgc2V0RGF0YTogUmVhY3QuRGlzcGF0Y2g8UmVhY3QuU2V0U3RhdGVBY3Rpb248SVN1cHBsaWVyQ29udGFjdFtdPj5cclxufVxyXG5cclxuZnVuY3Rpb24gU3VwcGxpZXJDb250YWN0cyh7IGRhdGEsIHNldERhdGEgfTogSUNvbXBvbmVudFByb3BzKSB7XHJcbiAgICBjb25zdCBhZGRJdGVtID0gKCkgPT4ge1xyXG4gICAgICAgIHNldERhdGEoKHByZXYpID0+IHtcclxuICAgICAgICAgICAgcmV0dXJuIFtcclxuICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgbmFtZTogJycsXHJcbiAgICAgICAgICAgICAgICAgICAgcGhvbmU6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgIGVtYWlsOiAnJyxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIF1cclxuICAgICAgICB9KVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHJlbW92ZUl0ZW0gPSAoaW5kZXg6IG51bWJlcikgPT4ge1xyXG4gICAgICAgIHNldERhdGEoKHByZXYpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgbmV3RGF0YSA9IFsuLi5wcmV2XVxyXG4gICAgICAgICAgICBuZXdEYXRhLnNwbGljZShpbmRleCwgMSlcclxuICAgICAgICAgICAgcmV0dXJuIG5ld0RhdGFcclxuICAgICAgICB9KVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IG9uVmFsdWVDaGFuZ2UgPSA8SyBleHRlbmRzIGtleW9mIElTdXBwbGllckNvbnRhY3Q+KFxyXG4gICAgICAgIGZpZWxkOiBLLFxyXG4gICAgICAgIGluZGV4OiBudW1iZXIsXHJcbiAgICAgICAgdmFsdWU6IElTdXBwbGllckNvbnRhY3RbS10sXHJcbiAgICApID0+IHtcclxuICAgICAgICBzZXREYXRhKChwcmV2KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IG5ld0RhdGEgPSBbLi4ucHJldl1cclxuXHJcbiAgICAgICAgICAgIG5ld0RhdGFbaW5kZXhdW2ZpZWxkXSA9IHZhbHVlXHJcblxyXG4gICAgICAgICAgICByZXR1cm4gbmV3RGF0YVxyXG4gICAgICAgIH0pXHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTYgcGItNCBwdC0zIHB4LTRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhsXCI+XHJcbiAgICAgICAgICAgICAgICBDb250YWN0IHBlb3BsZVxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyBtdC00IG1heC13LVsyNXJlbV0gbGVhZGluZy1sb29zZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIEVudGVyIHRoZSBjb250YWN0IGRldGFpbHMgKG5hbWUsIHBob25lLCBhbmQgZW1haWwpIG9mIHRoZVxyXG4gICAgICAgICAgICAgICAgICAgIHN1cHBsaWVy4oCZcyByZXByZXNlbnRhdGl2ZS5cclxuICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWQ6Y29sLXNwYW4tMlwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdyb3cgZmxleC1jb2xcIj5cclxuICAgICAgICAgICAgICAgICAgICB7ZGF0YS5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGdyb3cgZmxleC1jb2wgJHtpbmRleCA+IDAgPyAnYm9yZGVyLXQgbXQtNCBwdC00JyA6ICcnfWB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdyb3cgZmxleC1jb2wgbWQ6aXRlbXMtY2VudGVyIG1kOmZsZXgtcm93IG1kOmNvbC1zcGFuLTIgbGc6Y29sLXNwYW4tMyBsZzpncmlkLWNvbHMtMyBnYXAtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD17YHN1cHBsaWVyLWNvbnRhY3RfbmFtZSR7aW5kZXh9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkNvbnRhY3QgTmFtZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtpdGVtLm5hbWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uVmFsdWVDaGFuZ2UoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ25hbWUnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9e2BzdXBwbGllci1jb250YWN0X3Bob25lJHtpbmRleH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQ29udGFjdCBQaG9uZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtpdGVtLnBob25lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvblZhbHVlQ2hhbmdlKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdwaG9uZScsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXgsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZS50YXJnZXQudmFsdWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD17YHN1cHBsaWVyLWNvbnRhY3RfZW1haWwke2luZGV4fWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtpdGVtLmVtYWlsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkNvbnRhY3QgRW1haWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvblZhbHVlQ2hhbmdlKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdlbWFpbCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXgsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZS50YXJnZXQudmFsdWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtc20gZ2FwLTIgdGV4dC1yZWQtNjAwICR7aW5kZXggPT0gMCA/ICdtZDppbnZpc2libGUgaGlkZGVuIG1kOmZsZXgnIDogJ21kOnZpc2libGUgZmxleCd9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbmRleCA9PSAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAoKSA9PiBudWxsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAoKSA9PiByZW1vdmVJdGVtKGluZGV4KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz57JyAnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJibG9jayBtZDpoaWRkZW5cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFJlbW92ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBtdC00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgaW5saW5lLWZsZXgganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyIHJvdW5kZWQtbWQgYmctdHJhbnNwYXJlbnQgcHgtNCBweS0yIHRleHQtc20gdGV4dC1za3ktODAwIHNoYWRvdy1zbSBob3ZlcjpzaGFkb3ctbWQgaG92ZXI6Ymctd2hpdGUgcmluZy0xIHJpbmctc2t5LTcwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2FkZEl0ZW19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8UGx1c0ljb24gY2xhc3NOYW1lPVwidy01IGgtNSAtbWwtMiBtci0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgQWRkIGNvbnRhY3RcclxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgIClcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgU3VwcGxpZXJDb250YWN0c1xyXG4iXSwibmFtZXMiOlsiWCIsIklucHV0IiwiQnV0dG9uIiwiU3VwcGxpZXJDb250YWN0cyIsImRhdGEiLCJzZXREYXRhIiwiYWRkSXRlbSIsInByZXYiLCJuYW1lIiwicGhvbmUiLCJlbWFpbCIsInJlbW92ZUl0ZW0iLCJpbmRleCIsIm5ld0RhdGEiLCJzcGxpY2UiLCJvblZhbHVlQ2hhbmdlIiwiZmllbGQiLCJ2YWx1ZSIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJtYXAiLCJpdGVtIiwiaWQiLCJ0eXBlIiwicGxhY2Vob2xkZXIiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJvbkNsaWNrIiwic3BhbiIsIlBsdXNJY29uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/inventory/supplier-contacts.tsx\n"));

/***/ })

});