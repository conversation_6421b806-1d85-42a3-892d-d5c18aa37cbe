"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/suppliers/new/page",{

/***/ "(app-pages-browser)/./src/app/ui/inventory/supplier-contacts.tsx":
/*!****************************************************!*\
  !*** ./src/app/ui/inventory/supplier-contacts.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n\n\n\n\n\nfunction SupplierContacts(param) {\n    let { data, setData } = param;\n    const addItem = ()=>{\n        setData((prev)=>{\n            return [\n                ...prev,\n                {\n                    name: \"\",\n                    phone: \"\",\n                    email: \"\"\n                }\n            ];\n        });\n    };\n    const removeItem = (index)=>{\n        setData((prev)=>{\n            const newData = [\n                ...prev\n            ];\n            newData.splice(index, 1);\n            return newData;\n        });\n    };\n    const onValueChange = (field, index, value)=>{\n        setData((prev)=>{\n            const newData = [\n                ...prev\n            ];\n            newData[index][field] = value;\n            return newData;\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-5\",\n        children: [\n            data.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 \".concat(index > 0 ? \"border-t pt-4\" : \"\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                    label: \"Contact Name\",\n                                    htmlFor: \"supplier-contact_name\".concat(index),\n                                    className: \"text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                        id: \"supplier-contact_name\".concat(index),\n                                        type: \"text\",\n                                        placeholder: \"Contact Name\",\n                                        value: item.name,\n                                        onChange: (e)=>onValueChange(\"name\", index, e.target.value),\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                    label: \"Contact Phone\",\n                                    htmlFor: \"supplier-contact_phone\".concat(index),\n                                    className: \"text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                        id: \"supplier-contact_phone\".concat(index),\n                                        type: \"text\",\n                                        placeholder: \"Contact Phone\",\n                                        value: item.phone,\n                                        onChange: (e)=>onValueChange(\"phone\", index, e.target.value),\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                    label: \"Contact Email\",\n                                    htmlFor: \"supplier-contact_email\".concat(index),\n                                    className: \"text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                        id: \"supplier-contact_email\".concat(index),\n                                        type: \"email\",\n                                        value: item.email,\n                                        placeholder: \"Contact Email\",\n                                        onChange: (e)=>onValueChange(\"email\", index, e.target.value),\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 21\n                        }, this),\n                        index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"destructive\",\n                                size: \"sm\",\n                                iconLeft: _barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                onClick: ()=>removeItem(index),\n                                children: \"Remove Contact\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, index, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 17\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end mt-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"outline\",\n                    iconLeft: _barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    onClick: addItem,\n                    children: \"Add Contact\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n                lineNumber: 136,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-contacts.tsx\",\n        lineNumber: 55,\n        columnNumber: 9\n    }, this);\n}\n_c = SupplierContacts;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SupplierContacts);\nvar _c;\n$RefreshReg$(_c, \"SupplierContacts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/inventory/supplier-contacts.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/ui/inventory/supplier-new.tsx":
/*!***********************************************!*\
  !*** ./src/app/ui/inventory/supplier-new.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewSupplier; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building2_FileText_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building2,FileText,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building2_FileText_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building2,FileText,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building2_FileText_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building2,FileText,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building2_FileText_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building2,FileText,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building2_FileText_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building2,FileText,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation_CREATE_SUPPLIER_CONTACT__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/lib/graphQL/mutation/CREATE_SUPPLIER_CONTACT */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/CREATE_SUPPLIER_CONTACT.ts\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _supplier_contacts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./supplier-contacts */ \"(app-pages-browser)/./src/app/ui/inventory/supplier-contacts.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewSupplier(param) {\n    let { supplierId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [fileLinks, setFileLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [linkSelectedOption, setLinkSelectedOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [contactFields, setContactFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            name: \"\",\n            email: \"\",\n            phone: \"\"\n        }\n    ]);\n    const handleCreate = async ()=>{\n        var _response_data;\n        const name = document.getElementById(\"supplier-name\").value;\n        const website = document.getElementById(\"supplier-website\").value;\n        const phone = document.getElementById(\"supplier-phone\").value;\n        const email = document.getElementById(\"supplier-email\").value;\n        const address = document.getElementById(\"supplier-address\").value;\n        const notes = document.getElementById(\"supplier-notes\").value;\n        const variables = {\n            input: {\n                name,\n                address,\n                website,\n                email,\n                phone,\n                notes\n            }\n        };\n        if (name === \"\") {\n            return toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Please fill supplier's name!\"\n            });\n        }\n        if (contactFields.length > 0) {\n            const anyContactFieldEmpty = contactFields.filter((contactField)=>contactField.name == \"\" || contactField.phone == \"\" && contactField.email == \"\").length;\n            if (anyContactFieldEmpty > 0) {\n                return toast({\n                    variant: \"destructive\",\n                    title: \"Error\",\n                    description: \"Please complete contact data!\"\n                });\n            }\n        }\n        const response = await mutationCreateSupplier({\n            variables\n        });\n        var _response_data_createSupplier_id;\n        const supplierID = (_response_data_createSupplier_id = (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.createSupplier.id) !== null && _response_data_createSupplier_id !== void 0 ? _response_data_createSupplier_id : 0;\n        if (supplierID == 0) {\n            return toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Error creating new supplier\"\n            });\n        }\n        contactFields.forEach(async (element)=>{\n            const variableContact = {\n                ...element,\n                supplierID\n            };\n            delete variableContact.id;\n            await mutationCreateSupplierContact({\n                variables: {\n                    input: variableContact\n                }\n            });\n        });\n        router.back();\n    };\n    const [mutationCreateSupplier, { loading: mutationcreateSupplierLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_SUPPLIER, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"mutationcreateSupplier error\", error);\n        }\n    });\n    const [mutationCreateSupplierContact, { loading: mutationcreateSupplierContactLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation)(_app_lib_graphQL_mutation_CREATE_SUPPLIER_CONTACT__WEBPACK_IMPORTED_MODULE_11__.CREATE_SUPPLIER_CONTACT, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"mutationcreateSupplierContact error\", error);\n        }\n    });\n    const [createSeaLogsFileLinks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_9__.CREATE_SEALOGS_FILE_LINKS, {\n        onCompleted: (response)=>{\n            const data = response.createSeaLogsFileLinks;\n            if (data.id > 0) {\n                const newLinks = [\n                    ...fileLinks,\n                    data\n                ];\n                setFileLinks(newLinks);\n                linkSelectedOption ? setLinkSelectedOption([\n                    ...linkSelectedOption,\n                    {\n                        label: data.link,\n                        value: data.id\n                    }\n                ]) : setLinkSelectedOption([\n                    {\n                        label: data.link,\n                        value: data.id\n                    }\n                ]);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createSeaLogsFileLinksEntry error\", error);\n        }\n    });\n    const handleDeleteLink = (link)=>{\n        setLinkSelectedOption(linkSelectedOption.filter((l)=>l !== link));\n    };\n    const linkItem = (link)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-between align-middle mr-2 w-fit\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    href: link.label,\n                    target: \"_blank\",\n                    className: \"ml-2 \",\n                    children: link.label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-2 \",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Building2_FileText_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                        iconOnly: true,\n                        onClick: ()=>handleDeleteLink(link)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n            lineNumber: 165,\n            columnNumber: 13\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Card, {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building2_FileText_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.H4, {\n                                    children: \"New Supplier\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium flex items-center gap-2 text-primary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building2_FileText_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 29\n                                            }, this),\n                                            \"Company Information\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground text-sm leading-relaxed\",\n                                        children: \"Enter the basic information about the supplier company including contact details and address.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2 space-y-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        label: \"Company Name\",\n                                        htmlFor: \"supplier-name\",\n                                        className: \"text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            id: \"supplier-name\",\n                                            type: \"text\",\n                                            placeholder: \"Supplier name\",\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        label: \"Website\",\n                                        htmlFor: \"supplier-website\",\n                                        className: \"text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            id: \"supplier-website\",\n                                            type: \"text\",\n                                            placeholder: \"Company website\",\n                                            className: \"w-full\",\n                                            onKeyDown: async (event)=>{\n                                                if (event.key === \"Enter\") {\n                                                    const inputValue = event.target.value;\n                                                    await createSeaLogsFileLinks({\n                                                        variables: {\n                                                            input: {\n                                                                link: inputValue\n                                                            }\n                                                        }\n                                                    });\n                                                    toast({\n                                                        title: \"Website added\",\n                                                        description: \"Added \".concat(inputValue, \" to supplier links\")\n                                                    });\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 25\n                                    }, this),\n                                    (linkSelectedOption || fileLinks).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            label: \"Linked Websites\",\n                                            className: \"text-sm font-medium\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: linkSelectedOption ? linkSelectedOption.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-block\",\n                                                        children: linkItem(link)\n                                                    }, link.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 55\n                                                    }, this)) : fileLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-block\",\n                                                        children: linkItem(link)\n                                                    }, link.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 55\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                label: \"Phone Number\",\n                                                htmlFor: \"supplier-phone\",\n                                                className: \"text-sm font-medium\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    id: \"supplier-phone\",\n                                                    type: \"text\",\n                                                    placeholder: \"Phone number\",\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 33\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                label: \"Email Address\",\n                                                htmlFor: \"supplier-email\",\n                                                className: \"text-sm font-medium\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    id: \"supplier-email\",\n                                                    type: \"email\",\n                                                    placeholder: \"Email address\",\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 33\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        label: \"Address\",\n                                        htmlFor: \"supplier-address\",\n                                        className: \"text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            id: \"supplier-address\",\n                                            rows: 3,\n                                            placeholder: \"Supplier address\",\n                                            className: \"w-full resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium flex items-center gap-2 text-primary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building2_FileText_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 29\n                                            }, this),\n                                            \"Contact Persons\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground text-sm leading-relaxed\",\n                                        children: \"Enter the contact details (name, phone, and email) of the supplier's representative.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_supplier_contacts__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    data: contactFields,\n                                    setData: setContactFields\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium flex items-center gap-2 text-primary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building2_FileText_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 29\n                                            }, this),\n                                            \"Additional Notes\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground text-sm leading-relaxed\",\n                                        children: \"Add any additional information about this supplier that might be useful.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                    label: \"Notes\",\n                                    htmlFor: \"supplier-notes\",\n                                    className: \"text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        id: \"supplier-notes\",\n                                        rows: 5,\n                                        placeholder: \"Enter any additional notes about this supplier...\",\n                                        className: \"w-full resize-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                lineNumber: 184,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_12__.FooterWrapper, {\n                className: \"mt-5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Building2_FileText_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                        onClick: ()=>router.back(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: handleCreate,\n                        children: \"Create Supplier\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\supplier-new.tsx\",\n                lineNumber: 381,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NewSupplier, \"q/JrGpMPggXsrHp+ThyS7zkm6Zc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation\n    ];\n});\n_c = NewSupplier;\nvar _c;\n$RefreshReg$(_c, \"NewSupplier\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/inventory/supplier-new.tsx\n"));

/***/ })

});