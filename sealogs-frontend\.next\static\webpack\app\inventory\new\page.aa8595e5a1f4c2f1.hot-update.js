"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/new/page",{

/***/ "(app-pages-browser)/./src/components/file-upload.tsx":
/*!****************************************!*\
  !*** ./src/components/file-upload.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FileUpload; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction FileUpload(param) {\n    let { setDocuments, text = \"Documents and Images\", subText, bgClass = \"\", documents, multipleUpload = true } = param;\n    _s();\n    /* ------------------------------------------------------- */ /* state / refs                                            */ /* ------------------------------------------------------- */ const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentFiles, setCurrentFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openFileNameDialog, setOpenFileNameDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoader, setImageLoader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    /* ------------------------------------------------------- */ /* helpers                                                 */ /* ------------------------------------------------------- */ const dropZoneClasses = (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors focus-visible:outline-none\", dragActive ? \"bg-slorange-400/60 border-slorange-600\" : \"bg-slorange-300/40 border-slorange-1000\", \"text-slorange-1000 hover:bg-slorange-400/40\", \"min-h-[10rem] cursor-pointer select-none\", bgClass);\n    const uploadFile = async (file)=>{\n        const formData = new FormData();\n        formData.append(\"FileData\", file, file.name.replace(/\\s/g, \"\"));\n        try {\n            const response = await fetch(\"\".concat(\"https://api.sealogs.com/api/\", \"v2/upload\"), {\n                method: \"POST\",\n                headers: {\n                    Authorization: \"Bearer \".concat(localStorage.getItem(\"sl-jwt\"))\n                },\n                body: formData\n            });\n            const data = await response.json();\n            await getFileDetails({\n                variables: {\n                    id: [\n                        data[0].id\n                    ]\n                }\n            });\n            setImageLoader(false);\n        } catch (err) {\n            /* eslint-disable-next-line no-console */ console.error(err);\n        }\n    };\n    /* ------------------------------------------------------- */ /* apollo hooks                                            */ /* ------------------------------------------------------- */ const [getFileDetails] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.GET_FILES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setCurrentFiles((prev)=>[\n                    ...prev,\n                    response.readFiles.nodes[0]\n                ]);\n            setOpenFileNameDialog(true);\n        },\n        onError: (error)=>console.error(error)\n    });\n    const [updateFile] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UPDATE_FILE, {\n        onCompleted: (response)=>{\n            const updated = response.updateFile;\n            setDocuments((prev)=>multipleUpload ? [\n                    ...prev,\n                    updated\n                ] : [\n                    updated\n                ]);\n        },\n        onError: (error)=>console.error(error)\n    });\n    /* ------------------------------------------------------- */ /* event handlers                                          */ /* ------------------------------------------------------- */ const handleFiles = (fileList)=>{\n        const arr = Array.from(fileList);\n        setImageLoader(true);\n        arr.forEach(uploadFile);\n    };\n    const onChange = (e)=>{\n        if (e.target.files) handleFiles(e.target.files);\n    };\n    const onDrop = (e)=>{\n        e.preventDefault();\n        setDragActive(false);\n        if (e.dataTransfer.files) handleFiles(e.dataTransfer.files);\n    };\n    const onDragToggle = (state)=>(e)=>{\n            e.preventDefault();\n            setDragActive(state);\n        };\n    const handleUpdateFileName = ()=>{\n        currentFiles.forEach((file, index)=>{\n            const newFileName = document.getElementById(\"file-name-\".concat(index)).value;\n            updateFile({\n                variables: {\n                    input: {\n                        id: file.id,\n                        title: newFileName\n                    }\n                }\n            });\n        });\n        setOpenFileNameDialog(false);\n    };\n    const openFileExplorer = ()=>{\n        var _inputRef_current;\n        return (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.click();\n    };\n    /* ------------------------------------------------------- */ /* effects                                                 */ /* ------------------------------------------------------- */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (documents === null || documents === void 0 ? void 0 : documents.length) setFiles((prev)=>[\n                ...prev,\n                ...documents\n            ]);\n    }, [\n        documents\n    ]);\n    /* ------------------------------------------------------- */ /* render                                                  */ /* ------------------------------------------------------- */ return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full pt-4 lg:pt-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: dropZoneClasses,\n                onSubmit: (e)=>e.preventDefault(),\n                onDragEnter: onDragToggle(true),\n                onDragOver: onDragToggle(true),\n                onDragLeave: onDragToggle(false),\n                onDrop: onDrop,\n                \"aria-label\": \"File uploader drop zone\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute top-4 left-4 text-xs font-medium uppercase tracking-wider\",\n                        children: text\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                        ref: inputRef,\n                        type: \"file\",\n                        className: \"hidden\",\n                        multiple: multipleUpload,\n                        accept: \".xlsx,.xls,image/*,.doc,.docx,.ppt,.pptx,.txt,.pdf\",\n                        onChange: onChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        type: \"button\",\n                        variant: \"ghost\",\n                        onClick: openFileExplorer,\n                        className: \"flex flex-col items-center gap-2 h-auto p-0 hover:bg-transparent\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: \"/sealogs-document_upload.svg\",\n                                alt: \"Upload illustration\",\n                                width: 96,\n                                height: 96,\n                                priority: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 21\n                            }, this),\n                            subText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-slate-600\",\n                                children: subText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                lineNumber: 152,\n                columnNumber: 13\n            }, this),\n            imageLoader ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 flex items-center justify-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-5 w-5 animate-spin text-slorange-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-slorange-600\",\n                        children: \"Uploading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                lineNumber: 198,\n                columnNumber: 17\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.AlertDialogNew, {\n                openDialog: openFileNameDialog,\n                setOpenDialog: setOpenFileNameDialog,\n                handleCreate: handleUpdateFileName,\n                actionText: \"Save\",\n                title: \"File Name\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: currentFiles.map((file, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                            label: \"File \".concat(idx + 1, \" Name\"),\n                            htmlFor: \"file-name-\".concat(idx),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                id: \"file-name-\".concat(idx),\n                                defaultValue: file.title,\n                                placeholder: \"Enter file name\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 33\n                            }, this)\n                        }, file.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 29\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                lineNumber: 205,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n        lineNumber: 151,\n        columnNumber: 9\n    }, this);\n}\n_s(FileUpload, \"/F7rhqoG1tD93AQmvJO3DiL1QjM=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useMutation\n    ];\n});\n_c = FileUpload;\nvar _c;\n$RefreshReg$(_c, \"FileUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/file-upload.tsx\n"));

/***/ })

});